<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔镜换装 - 个人中心</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        *::-webkit-scrollbar {
            display: none;
        }
        
        body {
            overflow: hidden;
        }
        
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #ff006e, #8b5cf6);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 0 30px rgba(255, 0, 110, 0.4);
        }
        
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
        }
        
        .menu-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .menu-item:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }
        
        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        
        .vip-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .bottom-nav {
            background: rgba(255, 255, 255, 1);
            border-radius: 25px 25px 0 0;
            padding: 15px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            color: #00d4ff;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>
            
            <!-- 头部导航 -->
            <div class="flex items-center justify-between px-6 py-4">
                <h1 class="text-white text-lg font-semibold">个人中心</h1>
                <button class="text-white">
                    <i class="fas fa-cog text-xl"></i>
                </button>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="flex-1 px-6 pb-24 overflow-y-auto">
                <!-- 用户信息卡片 -->
                <div class="profile-header mb-6">
                    <div class="flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=80&h=80&fit=crop" 
                             alt="用户头像" class="avatar">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h2 class="text-white text-xl font-bold">时尚达人</h2>
                                <span class="vip-badge">VIP</span>
                            </div>
                            <p class="text-white/80 text-sm mb-2">ID: 123456789</p>
                            <p class="text-white/60 text-xs">注册时间：2024-01-01</p>
                        </div>
                        <button class="text-white">
                            <i class="fas fa-edit text-lg"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 数据统计 -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="stats-card">
                        <p class="text-white text-2xl font-bold mb-1">24</p>
                        <p class="text-gray-300 text-xs">换装次数</p>
                    </div>
                    <div class="stats-card">
                        <p class="text-white text-2xl font-bold mb-1">128</p>
                        <p class="text-gray-300 text-xs">剩余积分</p>
                    </div>
                    <div class="stats-card">
                        <p class="text-white text-2xl font-bold mb-1">5</p>
                        <p class="text-gray-300 text-xs">收藏作品</p>
                    </div>
                </div>
                
                <!-- 功能菜单 -->
                <div class="space-y-4">
                    <!-- 账户管理 -->
                    <div class="menu-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-white font-semibold">账户管理</h3>
                                    <p class="text-gray-300 text-sm">个人信息设置</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- 我的收藏 -->
                    <div class="menu-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-heart text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-white font-semibold">我的收藏</h3>
                                    <p class="text-gray-300 text-sm">收藏的换装作品</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- 积分商城 -->
                    <div class="menu-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-coins text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-white font-semibold">积分商城</h3>
                                    <p class="text-gray-300 text-sm">积分兑换礼品</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- 邀请好友 -->
                    <div class="menu-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-friends text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-white font-semibold">邀请好友</h3>
                                    <p class="text-gray-300 text-sm">邀请好友获得奖励</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- 客服支持 -->
                    <div class="menu-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-headset text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-white font-semibold">客服支持</h3>
                                    <p class="text-gray-300 text-sm">在线客服帮助</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- 关于我们 -->
                    <div class="menu-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-info-circle text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-white font-semibold">关于我们</h3>
                                    <p class="text-gray-300 text-sm">版本信息和反馈</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 退出登录 -->
                <div class="mt-8">
                    <button class="w-full bg-red-500/20 border border-red-500/50 text-red-400 py-3 rounded-lg font-semibold">
                        退出登录
                    </button>
                </div>
            </div>
            
            <!-- 底部导航 -->
            <div class="bottom-nav">
                <div class="flex">
                    <div class="nav-item">
                        <i class="fas fa-home"></i>
                        <div class="text-xs">首页</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-magic"></i>
                        <div class="text-xs">换装</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-history"></i>
                        <div class="text-xs">历史</div>
                    </div>
                    <div class="nav-item active">
                        <i class="fas fa-user"></i>
                        <div class="text-xs">我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
# 魔镜换装小程序 - 部署指南

## 🚀 部署前准备

### 1. 微信小程序账号准备

1. **注册小程序账号**
   - 访问 [微信公众平台](https://mp.weixin.qq.com/)
   - 选择 "小程序" 类型进行注册
   - 完成主体信息认证

2. **获取AppID**
   - 登录小程序后台
   - 在 "开发" → "开发设置" 中获取AppID
   - 将AppID填入 `project.config.json` 文件

### 2. 开发者权限配置

1. **添加开发者**
   - 在小程序后台 "管理" → "成员管理"
   - 添加项目开发者微信号
   - 分配适当的权限（开发者权限）

2. **服务器域名配置**
   ```
   request合法域名：
   https://api.mojing.com
   
   uploadFile合法域名：
   https://upload.mojing.com
   
   downloadFile合法域名：
   https://cdn.mojing.com
   ```

## 🛠 开发环境部署

### 1. 下载微信开发者工具

1. 访问 [官方下载页面](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 根据操作系统选择对应版本
3. 安装并启动开发者工具

### 2. 导入项目

1. **创建新项目**
   - 点击 "+" 创建新项目
   - 选择项目目录：`mini_program_project`
   - 输入AppID和项目名称
   - 选择 "不使用云服务"

2. **验证项目结构**
   ```
   mini_program_project/
   ├── app.js
   ├── app.json
   ├── app.wxss
   ├── pages/
   ├── project.config.json
   └── sitemap.json
   ```

### 3. 本地开发配置

1. **修改配置文件**
   ```json
   // project.config.json
   {
     "appid": "你的小程序AppID",
     "projectname": "魔镜换装"
   }
   ```

2. **开启开发模式**
   - 在开发者工具中点击 "详情"
   - 勾选 "不校验合法域名、web-view、TLS版本及HTTPS证书"
   - 开启 "ES6转ES5" 和 "样式自动补全"

## 📝 代码审查与优化

### 1. 代码检查清单

- [ ] **基础功能测试**
  - [ ] 页面跳转正常
  - [ ] 图片加载正常
  - [ ] 表单交互正常
  - [ ] 本地存储功能

- [ ] **性能优化**
  - [ ] 图片大小控制在500KB以内
  - [ ] 代码包总大小不超过2MB
  - [ ] 首屏加载时间控制在3秒内

- [ ] **兼容性测试**
  - [ ] 不同机型屏幕适配
  - [ ] iOS和Android系统测试
  - [ ] 微信版本兼容性

### 2. 性能优化建议

1. **图片优化**
   ```javascript
   // 使用懒加载
   <image lazy-load="true" src="{{imageUrl}}" />
   
   // 压缩图片尺寸
   chooseImage({
     sizeType: ['compressed']
   })
   ```

2. **代码分包**
   ```json
   // app.json
   "subpackages": [
     {
       "root": "packageA",
       "pages": [
         "pages/dressup/dressup"
       ]
     }
   ]
   ```

## 🔄 测试与验证

### 1. 功能测试

1. **基础流程测试**
   - 启动页 → 登录页 → 首页 → 换装页
   - 积分获取和消费流程
   - 历史记录查看和管理
   - 个人中心功能操作

2. **边界条件测试**
   - 网络异常处理
   - 积分不足提示
   - 图片上传失败重试
   - 数据为空的状态展示

### 2. 真机测试

1. **预览测试**
   - 点击开发者工具 "预览" 按钮
   - 使用微信扫码在真机上测试
   - 验证所有核心功能

2. **性能测试**
   - 检查内存使用情况
   - 监控网络请求性能
   - 测试在低性能设备上的表现

## 📤 版本发布流程

### 1. 上传代码

1. **准备发布版本**
   ```bash
   # 检查代码质量
   1. 清理调试代码和console.log
   2. 确认所有功能正常工作
   3. 更新版本号和changelog
   ```

2. **上传到微信后台**
   - 在开发者工具点击 "上传"
   - 填写版本号（如：1.0.0）
   - 添加版本描述和更新内容

### 2. 提交审核

1. **登录小程序后台**
   - 访问 [微信公众平台](https://mp.weixin.qq.com/)
   - 进入 "开发" → "开发管理"

2. **提交审核信息**
   ```
   版本描述：魔镜换装小程序 v1.0.0
   
   功能介绍：
   - AI智能换装功能
   - 积分系统和充值
   - 换装历史管理
   - 社交分享功能
   
   测试账号：
   微信号：test_account
   密码：test123
   ```

3. **审核注意事项**
   - 确保所有图片和文字符合规范
   - 提供完整的测试流程说明
   - 准备相关资质证明文件

### 3. 发布上线

1. **审核通过后操作**
   - 收到审核通过通知
   - 在后台点击 "发布" 按钮
   - 选择发布时间（立即发布或定时发布）

2. **发布后验证**
   - 在微信中搜索小程序名称
   - 验证线上版本功能正常
   - 监控用户反馈和数据

## ⚙️ 生产环境配置

### 1. 服务器域名配置

在小程序后台配置以下域名：

```
request合法域名：
https://api.mojing.com

socket合法域名：
wss://ws.mojing.com

uploadFile合法域名：
https://upload.mojing.com

downloadFile合法域名：
https://cdn.mojing.com
```

### 2. 业务域名配置

如果使用web-view组件：

```
业务域名：
https://h5.mojing.com
```

### 3. 支付配置

如果集成微信支付：

1. 开通微信支付商户号
2. 在小程序后台关联商户号
3. 配置支付回调域名

## 📊 数据统计与监控

### 1. 小程序数据助手

- 在微信中搜索 "小程序数据助手"
- 绑定小程序查看实时数据
- 监控用户访问量、留存率等指标

### 2. 自定义数据统计

```javascript
// 在关键页面添加数据统计
wx.reportAnalytics('page_view', {
  page: 'dressup',
  action: 'start_dressup'
})
```

### 3. 错误监控

```javascript
// app.js 中添加全局错误处理
App({
  onError(error) {
    // 上报错误信息到服务器
    console.error('小程序错误：', error)
  }
})
```

## 🔒 安全配置

### 1. 内容安全

- 启用内容安全API检测用户上传内容
- 过滤敏感词和违规图片
- 定期审查用户生成内容

### 2. 数据安全

```javascript
// 敏感数据加密存储
const encryptData = (data) => {
  // 使用加密算法处理敏感信息
  return encrypted
}
```

### 3. 接口安全

- 使用HTTPS协议
- 实施API接口签名验证
- 设置请求频率限制

## 🆙 版本更新策略

### 1. 版本号管理

采用语义化版本号：`主版本.次版本.修订版本`

- 主版本：重大功能更新
- 次版本：新增功能
- 修订版本：Bug修复

### 2. 发布节奏

- **热修复**：紧急Bug修复，24小时内发布
- **常规更新**：每2周发布一次
- **大版本更新**：每季度发布一次

### 3. 灰度发布

```javascript
// 使用灰度发布控制新功能
const enableNewFeature = () => {
  const userId = getCurrentUserId()
  return userId % 100 < 10 // 10%用户体验新功能
}
```

## ❗ 常见问题解决

### 1. 上传失败

**问题**：代码上传时提示包体积过大
**解决**：
- 压缩图片资源
- 清理无用代码和文件
- 使用代码分包

### 2. 审核被拒

**常见原因**：
- 功能描述不清晰
- 缺少必要的用户协议
- 内容不符合平台规范

**解决方案**：
- 完善功能描述和使用说明
- 添加用户协议和隐私政策
- 确保内容合规

### 3. 性能问题

**问题**：小程序运行卡顿
**解决**：
- 优化图片加载策略
- 减少DOM操作频率
- 使用合适的数据结构

## 📞 技术支持

如果在部署过程中遇到问题，请联系：

- **技术支持**：<EMAIL>
- **商务合作**：<EMAIL>
- **用户反馈**：<EMAIL>

---

**最后更新时间**：2024年1月  
**文档版本**：v1.0
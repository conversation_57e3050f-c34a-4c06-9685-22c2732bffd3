<!--pages/dressup/dressup.wxml-->
<view class="container">
  <!-- 头部导航 -->
  <view class="nav-header">
    <text class="iconfont icon-back nav-back" bindtap="goBack"></text>
    <text class="nav-title">AI换装</text>
    <view style="width: 40rpx;"></view>
  </view>
  
  <!-- 步骤指示器 -->
  <view class="step-indicator">
    <view class="step-item {{currentStep >= 1 ? 'active' : ''}}">
      <view class="step-number">1</view>
      <view class="step-text">上传自拍</view>
    </view>
    <view class="step-line {{currentStep >= 2 ? 'active' : ''}}"></view>
    <view class="step-item {{currentStep >= 2 ? 'active' : ''}}">
      <view class="step-number">2</view>
      <view class="step-text">选择服装</view>
    </view>
    <view class="step-line {{currentStep >= 3 ? 'active' : ''}}"></view>
    <view class="step-item {{currentStep >= 3 ? 'active' : ''}}">
      <view class="step-number">3</view>
      <view class="step-text">生成效果</view>
    </view>
  </view>
  
  <!-- 主要内容区域 -->
  <scroll-view class="content" scroll-y>
    <!-- 自拍上传区域 -->
    <view class="upload-section">
      <view class="section-title">上传你的自拍照</view>
      <view class="upload-area {{selfieImage ? 'has-image' : ''}}" bindtap="chooseSelfieImage">
        <image wx:if="{{selfieImage}}" class="upload-image" src="{{selfieImage}}" mode="aspectFill" />
        <view wx:else class="upload-placeholder">
          <text class="iconfont icon-camera upload-icon"></text>
          <view class="upload-text">点击上传自拍照</view>
          <view class="upload-desc">建议上传清晰的全身或半身照</view>
        </view>
      </view>
    </view>
    
    <!-- 服装上传区域 -->
    <view class="upload-section">
      <view class="section-title">上传服装图片</view>
      <view class="upload-area {{clothImage ? 'has-image' : ''}}" bindtap="chooseClothImage">
        <image wx:if="{{clothImage}}" class="upload-image" src="{{clothImage}}" mode="aspectFill" />
        <view wx:else class="upload-placeholder">
          <text class="iconfont icon-tshirt upload-icon"></text>
          <view class="upload-text">点击上传服装图片</view>
          <view class="upload-desc">从电商网站保存的服装图片</view>
        </view>
      </view>
    </view>
    
    <!-- 积分消耗提示 -->
    <view class="points-tip">
      <view class="tip-content">
        <text class="iconfont icon-coins tip-icon"></text>
        <view class="tip-text">
          <view class="tip-title">本次换装消耗</view>
          <view class="tip-desc">基础换装：1积分 | 高清换装：2积分</view>
        </view>
      </view>
    </view>
    
    <!-- 处理按钮 -->
    <view class="action-buttons" wx:if="{{!isProcessing && !resultImage}}">
      <button 
        class="process-button basic" 
        disabled="{{!canProcess}}"
        bindtap="startProcessing"
        data-type="basic"
      >
        <text class="iconfont icon-magic"></text>
        开始AI换装 (1积分)
      </button>
      
      <button 
        class="process-button premium" 
        disabled="{{!canProcess}}"
        bindtap="startProcessing"
        data-type="premium"
      >
        <text class="iconfont icon-star"></text>
        高清换装 (2积分)
      </button>
    </view>
    
    <!-- 处理中状态 -->
    <view class="processing-state" wx:if="{{isProcessing}}">
      <view class="loading-ring"></view>
      <view class="processing-title">AI正在处理中...</view>
      <view class="processing-desc">预计需要3-5秒</view>
      <view class="processing-progress">
        <view class="progress-bar" style="width: {{progress}}%;"></view>
      </view>
    </view>
    
    <!-- 结果展示 -->
    <view class="result-section" wx:if="{{resultImage}}">
      <view class="section-title">换装效果</view>
      <view class="result-image-container">
        <image class="result-image" src="{{resultImage}}" mode="aspectFill" />
      </view>
      
      <view class="result-actions">
        <button class="action-btn secondary" bindtap="retryProcessing">
          <text class="iconfont icon-retry"></text>
          重新换装
        </button>
        <button class="action-btn primary" bindtap="saveWork">
          <text class="iconfont icon-save"></text>
          保存作品
        </button>
      </view>
      
      <view class="share-section">
        <button class="share-btn" bindtap="shareWork">
          <text class="iconfont icon-share"></text>
          分享作品
        </button>
      </view>
    </view>
  </scroll-view>
</view>
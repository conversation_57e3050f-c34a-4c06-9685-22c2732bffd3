<!--pages/points/points.wxml-->
<view class="container">
  <!-- 头部导航 -->
  <view class="nav-header">
    <text class="iconfont icon-back nav-back" bindtap="goBack"></text>
    <text class="nav-title">积分中心</text>
    <view style="width: 40rpx;"></view>
  </view>
  
  <!-- 积分余额卡片 -->
  <view class="points-card">
    <text class="iconfont icon-coins points-icon"></text>
    <view class="points-amount">{{userPoints}}</view>
    <view class="points-label">当前积分余额</view>
  </view>
  
  <!-- 内容区域 -->
  <scroll-view class="content" scroll-y>
    <!-- 免费获取积分 -->
    <view class="section">
      <view class="section-title">免费获取积分</view>
      <view class="ad-button" bindtap="watchAd">
        <view class="ad-content">
          <view class="ad-left">
            <text class="iconfont icon-play ad-icon"></text>
            <view class="ad-text">
              <view class="ad-title">观看广告</view>
              <view class="ad-desc">观看15秒广告获得1积分</view>
            </view>
          </view>
          <view class="ad-reward">+1积分</view>
        </view>
      </view>
    </view>
    
    <!-- 充值套餐 -->
    <view class="section">
      <view class="section-title">充值套餐</view>
      <view class="recharge-list">
        <view 
          class="recharge-item" 
          wx:for="{{rechargePackages}}" 
          wx:key="id"
          bindtap="purchasePackage"
          data-package="{{item}}"
        >
          <view class="package-info">
            <view class="package-header">
              <view class="package-title">{{item.points}}积分</view>
              <view class="package-badge" wx:if="{{item.popular}}">热门</view>
            </view>
            <view class="package-desc">{{item.description}}</view>
          </view>
          <view class="package-price">
            <view class="price-amount">¥{{item.price}}</view>
            <view class="price-unit">¥{{item.unitPrice}}/积分</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 积分使用说明 -->
    <view class="section">
      <view class="section-title">积分使用说明</view>
      <view class="usage-list">
        <view class="usage-item">
          <text class="iconfont icon-magic usage-icon"></text>
          <text class="usage-text">基础换装：1积分/次</text>
        </view>
        <view class="usage-item">
          <text class="iconfont icon-star usage-icon"></text>
          <text class="usage-text">高清换装：2积分/次</text>
        </view>
        <view class="usage-item">
          <text class="iconfont icon-layers usage-icon"></text>
          <text class="usage-text">批量换装：5积分/次</text>
        </view>
      </view>
    </view>
    
    <!-- 消费记录 -->
    <view class="section">
      <view class="section-title">最近消费记录</view>
      <view class="record-list">
        <view 
          class="record-item" 
          wx:for="{{consumeRecords}}" 
          wx:key="id"
        >
          <view class="record-info">
            <view class="record-title">{{item.title}}</view>
            <view class="record-time">{{item.time}}</view>
          </view>
          <view class="record-amount {{item.type === 'consume' ? 'negative' : 'positive'}}">
            {{item.type === 'consume' ? '-' : '+'}}{{item.amount}}积分
          </view>
        </view>
        
        <view class="record-empty" wx:if="{{consumeRecords.length === 0}}">
          <text class="iconfont icon-empty"></text>
          <view class="empty-text">暂无消费记录</view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
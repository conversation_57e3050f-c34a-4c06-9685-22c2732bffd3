// pages/splash/splash.js
Page({
  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 模拟启动加载时间
    setTimeout(() => {
      this.checkLoginStatus()
    }, 2000)
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    
    // 检查本地存储的登录状态
    const isLogin = wx.getStorageSync('isLogin') || false
    
    if (isLogin) {
      // 已登录，跳转到首页
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    } else {
      // 未登录，跳转到登录页
      wx.redirectTo({
        url: '/pages/login/login'
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '魔镜换装 - AI智能换装应用',
      path: '/pages/splash/splash'
    }
  }
})
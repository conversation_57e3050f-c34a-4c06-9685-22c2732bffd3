<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔镜换装 - 登录注册</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        *::-webkit-scrollbar {
            display: none;
        }
        
        body {
            overflow: hidden;
        }
        
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .neon-button {
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            color: white;
            font-weight: bold;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .neon-button:hover {
            box-shadow: 0 0 30px rgba(255, 0, 110, 0.7);
            transform: translateY(-2px);
        }
        
        .neon-input {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 15px 20px;
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .neon-input:focus {
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
            outline: none;
        }
        
        .neon-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .social-button {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 12px;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .social-button:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="flex-1 px-8 py-6">
                <!-- Logo区域 -->
                <div class="text-center mb-12 mt-8">
                    <div class="mb-4">
                        <i class="fas fa-magic text-5xl text-purple-400"></i>
                    </div>
                    <h1 class="logo-text mb-2">魔镜换装</h1>
                    <p class="text-gray-300 text-sm">开启你的时尚之旅</p>
                </div>
                
                <!-- 登录表单 -->
                <div class="space-y-6">
                    <div>
                        <input type="tel" placeholder="请输入手机号" 
                               class="neon-input w-full text-white">
                    </div>
                    
                    <div class="relative">
                        <input type="password" placeholder="请输入验证码" 
                               class="neon-input w-full text-white pr-24">
                        <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 text-sm">
                            获取验证码
                        </button>
                    </div>
                    
                    <!-- 登录按钮 -->
                    <button class="neon-button w-full text-lg">
                        立即登录
                    </button>
                    
                    <!-- 分割线 -->
                    <div class="flex items-center my-8">
                        <div class="flex-1 h-px bg-gray-600"></div>
                        <span class="px-4 text-gray-400 text-sm">或</span>
                        <div class="flex-1 h-px bg-gray-600"></div>
                    </div>
                    
                    <!-- 第三方登录 -->
                    <div class="space-y-4">
                        <button class="social-button w-full flex items-center justify-center space-x-3">
                            <i class="fab fa-weixin text-green-400 text-xl"></i>
                            <span>微信一键登录</span>
                        </button>
                    </div>
                    
                    <!-- 用户协议 -->
                    <div class="text-center mt-8">
                        <p class="text-gray-400 text-xs leading-relaxed">
                            登录即表示同意
                            <span class="text-blue-400">《用户协议》</span>
                            和
                            <span class="text-blue-400">《隐私政策》</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
// pages/dressup/dressup.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentStep: 1,
    selfieImage: '',
    clothImage: '',
    canProcess: false,
    isProcessing: false,
    progress: 0,
    resultImage: '',
    processingType: 'basic' // basic 或 premium
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkProcessStatus()
  },

  /**
   * 检查是否可以开始处理
   */
  checkProcessStatus() {
    const { selfieImage, clothImage } = this.data
    const canProcess = selfieImage && clothImage
    
    let currentStep = 1
    if (selfieImage) currentStep = 2
    if (selfieImage && clothImage) currentStep = 3
    
    this.setData({
      canProcess,
      currentStep
    })
  },

  /**
   * 选择自拍照片
   */
  chooseSelfieImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          selfieImage: res.tempFilePaths[0]
        })
        this.checkProcessStatus()
      },
      fail: (err) => {
        console.error('选择图片失败', err)
      }
    })
  },

  /**
   * 选择服装图片
   */
  chooseClothImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        this.setData({
          clothImage: res.tempFilePaths[0]
        })
        this.checkProcessStatus()
      },
      fail: (err) => {
        console.error('选择图片失败', err)
      }
    })
  },

  /**
   * 开始处理
   */
  startProcessing(e) {
    const type = e.currentTarget.dataset.type
    const app = getApp()
    
    // 检查积分是否足够
    const requiredPoints = type === 'basic' ? 1 : 2
    const userPoints = app.getUserPoints()
    
    if (userPoints < requiredPoints) {
      wx.showModal({
        title: '积分不足',
        content: `需要${requiredPoints}积分，当前只有${userPoints}积分。是否前往充值？`,
        confirmText: '去充值',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/points/points'
            })
          }
        }
      })
      return
    }

    // 确认消费积分
    wx.showModal({
      title: '确认换装',
      content: `本次${type === 'basic' ? '基础' : '高清'}换装将消耗${requiredPoints}积分，是否继续？`,
      success: (res) => {
        if (res.confirm) {
          this.performProcessing(type, requiredPoints)
        }
      }
    })
  },

  /**
   * 执行处理
   */
  performProcessing(type, points) {
    const app = getApp()
    
    // 消费积分
    if (!app.consumePoints(points)) {
      app.showToast('积分不足', 'error')
      return
    }

    this.setData({
      isProcessing: true,
      progress: 0,
      processingType: type
    })

    // 模拟处理进度
    this.simulateProgress()
  },

  /**
   * 模拟处理进度
   */
  simulateProgress() {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 15
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
        
        // 处理完成
        setTimeout(() => {
          this.processComplete()
        }, 500)
      }
      
      this.setData({
        progress: progress
      })
    }, 200)
  },

  /**
   * 处理完成
   */
  processComplete() {
    // 模拟生成结果图片
    const resultImages = [
      'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=400&h=600&fit=crop',
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=600&fit=crop',
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=600&fit=crop'
    ]
    
    const randomImage = resultImages[Math.floor(Math.random() * resultImages.length)]
    
    this.setData({
      isProcessing: false,
      resultImage: randomImage,
      currentStep: 3
    })

    const app = getApp()
    app.showToast('换装完成！', 'success')
  },

  /**
   * 重新换装
   */
  retryProcessing() {
    this.setData({
      resultImage: '',
      isProcessing: false,
      progress: 0,
      currentStep: this.data.selfieImage && this.data.clothImage ? 3 : (this.data.selfieImage ? 2 : 1)
    })
  },

  /**
   * 保存作品
   */
  saveWork() {
    const app = getApp()
    
    // 模拟保存到相册
    wx.saveImageToPhotosAlbum({
      filePath: this.data.resultImage,
      success: () => {
        app.showToast('保存成功！', 'success')
        
        // 保存到历史记录
        this.saveToHistory()
      },
      fail: (err) => {
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要授权',
            content: '保存图片需要访问您的相册权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
            }
          })
        } else {
          app.showToast('保存失败', 'error')
        }
      }
    })
  },

  /**
   * 保存到历史记录
   */
  saveToHistory() {
    const historyList = wx.getStorageSync('dressupHistory') || []
    const newRecord = {
      id: Date.now(),
      selfieImage: this.data.selfieImage,
      clothImage: this.data.clothImage,
      resultImage: this.data.resultImage,
      type: this.data.processingType,
      createTime: new Date().toISOString(),
      title: `${this.data.processingType === 'basic' ? '基础' : '高清'}换装作品`
    }
    
    historyList.unshift(newRecord)
    
    // 只保留最近100条记录
    if (historyList.length > 100) {
      historyList.splice(100)
    }
    
    wx.setStorageSync('dressupHistory', historyList)
  },

  /**
   * 分享作品
   */
  shareWork() {
    wx.showActionSheet({
      itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
      success: (res) => {
        const app = getApp()
        switch (res.tapIndex) {
          case 0:
            app.showToast('分享到微信成功！', 'success')
            break
          case 1:
            app.showToast('分享到朋友圈成功！', 'success')
            break
          case 2:
            wx.setClipboardData({
              data: '魔镜换装作品分享链接',
              success: () => {
                app.showToast('链接已复制！', 'success')
              }
            })
            break
        }
      }
    })
  },

  /**
   * 返回
   */
  goBack() {
    if (this.data.isProcessing) {
      wx.showModal({
        title: '确认离开',
        content: '正在处理中，确认要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack()
          }
        }
      })
    } else {
      wx.navigateBack()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    if (this.data.resultImage) {
      return {
        title: '看看我的AI换装效果！',
        path: '/pages/dressup/dressup',
        imageUrl: this.data.resultImage
      }
    }
    
    return {
      title: '魔镜换装 - AI智能换装应用',
      path: '/pages/dressup/dressup'
    }
  }
})
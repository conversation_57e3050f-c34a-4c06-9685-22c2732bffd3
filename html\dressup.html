<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔镜换装 - 换装页面</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        *::-webkit-scrollbar {
            display: none;
        }
        
        body {
            overflow: hidden;
        }
        
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .upload-area {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(0, 212, 255, 0.5);
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        
        .upload-area.has-image {
            border-style: solid;
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .process-button {
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            border-radius: 25px;
            padding: 15px 30px;
            color: white;
            font-weight: bold;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .process-button:hover {
            box-shadow: 0 0 30px rgba(255, 0, 110, 0.7);
            transform: translateY(-2px);
        }
        
        .process-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(0, 212, 255, 0.3);
            border-top: 4px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result-image {
            border-radius: 15px;
            box-shadow: 0 0 25px rgba(0, 212, 255, 0.4);
        }
        
        .bottom-nav {
            background: rgba(255, 255, 255, 1);
            border-radius: 25px 25px 0 0;
            padding: 15px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            color: #00d4ff;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>
            
            <!-- 头部导航 -->
            <div class="flex items-center justify-between px-6 py-4">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-white text-lg font-semibold">AI换装</h1>
                <div class="w-6"></div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="flex-1 px-6 pb-24 overflow-y-auto">
                <!-- 步骤指示器 -->
                <div class="flex items-center justify-center mb-6">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                            <span class="ml-2 text-white text-sm">上传自拍</span>
                        </div>
                        <div class="w-8 h-px bg-gray-600"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                            <span class="ml-2 text-gray-400 text-sm">选择服装</span>
                        </div>
                        <div class="w-8 h-px bg-gray-600"></div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                            <span class="ml-2 text-gray-400 text-sm">生成效果</span>
                        </div>
                    </div>
                </div>
                
                <!-- 自拍上传区域 -->
                <div class="mb-6">
                    <h2 class="text-white text-lg font-semibold mb-4">上传你的自拍照</h2>
                    <div class="upload-area">
                        <i class="fas fa-camera text-4xl text-blue-400 mb-4"></i>
                        <p class="text-white font-semibold mb-2">点击上传自拍照</p>
                        <p class="text-gray-300 text-sm">建议上传清晰的全身或半身照</p>
                    </div>
                </div>
                
                <!-- 服装上传区域 -->
                <div class="mb-6">
                    <h2 class="text-white text-lg font-semibold mb-4">上传服装图片</h2>
                    <div class="upload-area">
                        <i class="fas fa-tshirt text-4xl text-pink-400 mb-4"></i>
                        <p class="text-white font-semibold mb-2">点击上传服装图片</p>
                        <p class="text-gray-300 text-sm">从电商网站保存的服装图片</p>
                    </div>
                </div>
                
                <!-- 积分消耗提示 -->
                <div class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 rounded-lg p-4 mb-6">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-coins text-yellow-400 text-lg"></i>
                        <div>
                            <p class="text-white font-semibold">本次换装消耗</p>
                            <p class="text-gray-300 text-sm">基础换装：1积分 | 高清换装：2积分</p>
                        </div>
                    </div>
                </div>
                
                <!-- 处理按钮 -->
                <div class="space-y-4">
                    <button class="process-button w-full text-center" disabled>
                        <i class="fas fa-magic mr-2"></i>
                        开始AI换装 (1积分)
                    </button>
                    
                    <button class="process-button w-full text-center" disabled style="background: linear-gradient(45deg, #8b5cf6, #ec4899);">
                        <i class="fas fa-star mr-2"></i>
                        高清换装 (2积分)
                    </button>
                </div>
                
                <!-- 处理中状态 (隐藏) -->
                <div class="hidden text-center py-8">
                    <div class="progress-ring mx-auto mb-4"></div>
                    <p class="text-white text-lg font-semibold mb-2">AI正在处理中...</p>
                    <p class="text-gray-300 text-sm">预计需要3-5秒</p>
                </div>
                
                <!-- 结果展示 (隐藏) -->
                <div class="hidden">
                    <h2 class="text-white text-lg font-semibold mb-4">换装效果</h2>
                    <div class="result-image bg-gradient-to-br from-purple-500 to-pink-500 aspect-square rounded-lg flex items-center justify-center mb-6">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=300&h=400&fit=crop" 
                             alt="换装效果" class="w-full h-full object-cover rounded-lg">
                    </div>
                    
                    <div class="flex space-x-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold">
                            <i class="fas fa-redo mr-2"></i>
                            重新换装
                        </button>
                        <button class="flex-1 bg-gradient-to-r from-green-500 to-teal-500 text-white py-3 rounded-lg font-semibold">
                            <i class="fas fa-save mr-2"></i>
                            保存作品
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 底部导航 -->
            <div class="bottom-nav">
                <div class="flex">
                    <div class="nav-item">
                        <i class="fas fa-home"></i>
                        <div class="text-xs">首页</div>
                    </div>
                    <div class="nav-item active">
                        <i class="fas fa-magic"></i>
                        <div class="text-xs">换装</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-history"></i>
                        <div class="text-xs">历史</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-user"></i>
                        <div class="text-xs">我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
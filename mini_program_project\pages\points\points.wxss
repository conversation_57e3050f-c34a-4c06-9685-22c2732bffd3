/* pages/points/points.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 头部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
}

.nav-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.nav-back {
  color: white;
  font-size: 40rpx;
}

/* 积分卡片 */
.points-card {
  background: linear-gradient(135deg, #ff006e, #8b5cf6);
  border-radius: 40rpx;
  padding: 60rpx 40rpx;
  margin: 24rpx;
  text-align: center;
  box-shadow: 0 0 60rpx rgba(255, 0, 110, 0.4);
  box-sizing: border-box;
}

.points-icon {
  font-size: 80rpx;
  color: #ffd700;
  margin-bottom: 32rpx;
  display: block;
}

.points-amount {
  color: white;
  font-size: 96rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.points-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 36rpx;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 0 24rpx 32rpx;
  min-height: calc(100vh - 400rpx);
  box-sizing: border-box;
}

/* 通用section */
.section {
  margin-bottom: 48rpx;
}

.section-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
}

/* 广告按钮 */
.ad-button {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  border-radius: 30rpx;
  padding: 24rpx;
  box-shadow: 0 0 40rpx rgba(0, 212, 255, 0.5);
  box-sizing: border-box;
  width: 100%;
}

.ad-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ad-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.ad-icon {
  font-size: 48rpx;
  color: white;
  margin-right: 24rpx;
}

.ad-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.ad-desc {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
}

.ad-reward {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

/* 充值套餐 */
.recharge-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.recharge-item {
  background: rgba(255, 255, 255, 0.1);
  border: 4rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 30rpx;
  padding: 30rpx 24rpx;
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
}

.recharge-item:nth-child(2) {
  border-color: #ff006e;
  background: rgba(255, 0, 110, 0.1);
}

.package-info {
  flex: 1;
  min-width: 0;
}

.package-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.package-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.package-badge {
  background: #ff4444;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  flex-shrink: 0;
}

.package-desc {
  color: #cccccc;
  font-size: 28rpx;
}

.package-price {
  text-align: right;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.price-amount {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.price-unit {
  color: #888888;
  font-size: 24rpx;
}

/* 使用说明 */
.usage-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.usage-icon {
  font-size: 32rpx;
}

.usage-item:nth-child(1) .usage-icon {
  color: #00d4ff;
}

.usage-item:nth-child(2) .usage-icon {
  color: #8b5cf6;
}

.usage-item:nth-child(3) .usage-icon {
  color: #1aad19;
}

.usage-text {
  color: #cccccc;
  font-size: 28rpx;
}

/* 消费记录 */
.record-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.record-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
  padding: 24rpx;
  border-left: 8rpx solid #00d4ff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
}

.record-info {
  flex: 1;
  min-width: 0;
}

.record-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.record-time {
  color: #888888;
  font-size: 24rpx;
}

.record-amount {
  font-size: 32rpx;
  font-weight: 600;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.record-amount.positive {
  color: #1aad19;
}

.record-amount.negative {
  color: #ff4444;
}

/* 空状态 */
.record-empty {
  text-align: center;
  padding: 80rpx 0;
}

.icon-empty {
  font-size: 80rpx;
  color: #666666;
  margin-bottom: 24rpx;
  display: block;
}

.empty-text {
  color: #888888;
  font-size: 28rpx;
}

/* 图标字体 */
.icon-back::before { content: "←"; }
.icon-coins::before { content: "🪙"; }
.icon-play::before { content: "▶️"; }
.icon-magic::before { content: "✨"; }
.icon-star::before { content: "⭐"; }
.icon-layers::before { content: "📚"; }
.icon-empty::before { content: "📭"; }
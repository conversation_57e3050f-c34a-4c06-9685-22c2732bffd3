<!--pages/home/<USER>
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="header-left">
      <view class="app-title">魔镜换装</view>
      <view class="app-desc">让美丽触手可及</view>
    </view>
    <view class="points-badge" bindtap="goToPoints">
      <text class="iconfont icon-coins"></text>
      <text>{{userPoints}}积分</text>
    </view>
  </view>
  
  <!-- 主要CTA按钮 -->
  <view class="main-cta" bindtap="startDressup">
    <view class="cta-content">
      <text class="iconfont icon-magic cta-icon"></text>
      <view class="cta-text">
        <view class="cta-title">开始换装</view>
        <view class="cta-desc">上传照片，体验AI换装</view>
      </view>
    </view>
  </view>
  
  <!-- 每日签到 -->
  <view class="glass-card daily-checkin" bindtap="dailyCheckin">
    <view class="checkin-content">
      <view class="checkin-left">
        <view class="checkin-icon">
          <text class="iconfont icon-gift"></text>
        </view>
        <view class="checkin-text">
          <view class="checkin-title">每日签到</view>
          <view class="checkin-desc">签到获得免费积分</view>
        </view>
      </view>
      <button class="checkin-button" disabled="{{hasCheckedIn}}">
        {{hasCheckedIn ? '已签到' : '签到'}}
      </button>
    </view>
  </view>
  
  <!-- 功能卡片 -->
  <view class="feature-grid">
    <view class="feature-card" bindtap="goToHistory">
      <text class="iconfont icon-history feature-icon"></text>
      <view class="feature-title">换装历史</view>
      <view class="feature-desc">查看历史作品</view>
    </view>
    
    <view class="feature-card" bindtap="goToPoints">
      <text class="iconfont icon-coins feature-icon"></text>
      <view class="feature-title">积分中心</view>
      <view class="feature-desc">管理你的积分</view>
    </view>
  </view>
  
  <!-- 最近换装 -->
  <view class="glass-card recent-works">
    <view class="section-title">最近换装</view>
    <view class="works-grid">
      <view 
        class="work-item" 
        wx:for="{{recentWorks}}" 
        wx:key="id"
        bindtap="viewWork"
        data-work="{{item}}"
      >
        <image 
          class="work-image" 
          src="{{item.image}}" 
          mode="aspectFill"
          lazy-load
        />
      </view>
      <view class="work-item add-work" bindtap="startDressup">
        <text class="iconfont icon-plus"></text>
      </view>
    </view>
  </view>
</view>
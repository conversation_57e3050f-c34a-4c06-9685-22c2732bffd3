<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔镜换装 - 换装历史</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        *::-webkit-scrollbar {
            display: none;
        }
        
        body {
            overflow: hidden;
        }
        
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .history-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .history-item:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }
        
        .history-image {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.2);
        }
        
        .action-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }
        
        .action-button.share {
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            border: none;
        }
        
        .filter-tab {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            border-color: transparent;
        }
        
        .bottom-nav {
            background: rgba(255, 255, 255, 1);
            border-radius: 25px 25px 0 0;
            padding: 15px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            color: #00d4ff;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>
            
            <!-- 头部导航 -->
            <div class="flex items-center justify-between px-6 py-4">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-white text-lg font-semibold">换装历史</h1>
                <button class="text-white">
                    <i class="fas fa-search text-xl"></i>
                </button>
            </div>
            
            <!-- 筛选标签 -->
            <div class="px-6 mb-4">
                <div class="flex space-x-3 overflow-x-auto">
                    <button class="filter-tab active whitespace-nowrap">全部</button>
                    <button class="filter-tab whitespace-nowrap">本周</button>
                    <button class="filter-tab whitespace-nowrap">本月</button>
                    <button class="filter-tab whitespace-nowrap">已收藏</button>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="flex-1 px-6 pb-24 overflow-y-auto">
                <!-- 统计信息 -->
                <div class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div class="text-center">
                            <p class="text-white text-2xl font-bold">24</p>
                            <p class="text-gray-300 text-sm">总换装次数</p>
                        </div>
                        <div class="text-center">
                            <p class="text-white text-2xl font-bold">8</p>
                            <p class="text-gray-300 text-sm">本周换装</p>
                        </div>
                        <div class="text-center">
                            <p class="text-white text-2xl font-bold">5</p>
                            <p class="text-gray-300 text-sm">已收藏</p>
                        </div>
                    </div>
                </div>
                
                <!-- 历史记录列表 -->
                <div class="space-y-4">
                    <!-- 历史记录项1 -->
                    <div class="history-item">
                        <div class="flex space-x-4">
                            <div class="w-20 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center history-image">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=80&h=96&fit=crop" 
                                     alt="换装效果" class="w-full h-full object-cover rounded-lg">
                            </div>
                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div>
                                        <h3 class="text-white font-semibold">优雅连衣裙</h3>
                                        <p class="text-gray-300 text-sm">2024-01-15 14:30</p>
                                    </div>
                                    <button class="text-yellow-400">
                                        <i class="fas fa-star"></i>
                                    </button>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="action-button share">
                                        <i class="fas fa-share mr-1"></i>分享
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-edit mr-1"></i>重新编辑
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-download mr-1"></i>保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史记录项2 -->
                    <div class="history-item">
                        <div class="flex space-x-4">
                            <div class="w-20 h-24 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center history-image">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=96&fit=crop" 
                                     alt="换装效果" class="w-full h-full object-cover rounded-lg">
                            </div>
                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div>
                                        <h3 class="text-white font-semibold">职业套装</h3>
                                        <p class="text-gray-300 text-sm">2024-01-14 16:20</p>
                                    </div>
                                    <button class="text-gray-400">
                                        <i class="far fa-star"></i>
                                    </button>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="action-button share">
                                        <i class="fas fa-share mr-1"></i>分享
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-edit mr-1"></i>重新编辑
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-download mr-1"></i>保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史记录项3 -->
                    <div class="history-item">
                        <div class="flex space-x-4">
                            <div class="w-20 h-24 bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center history-image">
                                <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=80&h=96&fit=crop" 
                                     alt="换装效果" class="w-full h-full object-cover rounded-lg">
                            </div>
                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div>
                                        <h3 class="text-white font-semibold">休闲T恤</h3>
                                        <p class="text-gray-300 text-sm">2024-01-13 10:15</p>
                                    </div>
                                    <button class="text-yellow-400">
                                        <i class="fas fa-star"></i>
                                    </button>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="action-button share">
                                        <i class="fas fa-share mr-1"></i>分享
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-edit mr-1"></i>重新编辑
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-download mr-1"></i>保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史记录项4 -->
                    <div class="history-item">
                        <div class="flex space-x-4">
                            <div class="w-20 h-24 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center history-image">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=96&fit=crop" 
                                     alt="换装效果" class="w-full h-full object-cover rounded-lg">
                            </div>
                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div>
                                        <h3 class="text-white font-semibold">时尚外套</h3>
                                        <p class="text-gray-300 text-sm">2024-01-12 18:45</p>
                                    </div>
                                    <button class="text-gray-400">
                                        <i class="far fa-star"></i>
                                    </button>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="action-button share">
                                        <i class="fas fa-share mr-1"></i>分享
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-edit mr-1"></i>重新编辑
                                    </button>
                                    <button class="action-button">
                                        <i class="fas fa-download mr-1"></i>保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部导航 -->
            <div class="bottom-nav">
                <div class="flex">
                    <div class="nav-item">
                        <i class="fas fa-home"></i>
                        <div class="text-xs">首页</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-magic"></i>
                        <div class="text-xs">换装</div>
                    </div>
                    <div class="nav-item active">
                        <i class="fas fa-history"></i>
                        <div class="text-xs">历史</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-user"></i>
                        <div class="text-xs">我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔镜换装 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        *::-webkit-scrollbar {
            display: none;
        }
        
        body {
            overflow: hidden;
        }
        
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .main-cta {
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            border-radius: 30px;
            padding: 20px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .main-cta:hover {
            box-shadow: 0 0 40px rgba(255, 0, 110, 0.7);
            transform: translateY(-2px);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            padding: 20px;
        }
        
        .points-badge {
            background: linear-gradient(45deg, #ff006e, #8b5cf6);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            font-weight: bold;
            box-shadow: 0 0 15px rgba(255, 0, 110, 0.3);
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }
        
        .bottom-nav {
            background: rgba(255, 255, 255, 1);
            border-radius: 25px 25px 0 0;
            padding: 15px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            color: #00d4ff;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="flex-1 px-6 py-4 pb-24">
                <!-- 头部区域 -->
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h1 class="text-white text-2xl font-bold">魔镜换装</h1>
                        <p class="text-gray-300 text-sm">让美丽触手可及</p>
                    </div>
                    <div class="points-badge">
                        <i class="fas fa-coins mr-2"></i>
                        <span>128积分</span>
                    </div>
                </div>
                
                <!-- 主要CTA按钮 -->
                <div class="mb-8">
                    <button class="main-cta w-full text-center">
                        <div class="flex items-center justify-center space-x-4">
                            <i class="fas fa-magic text-3xl text-white"></i>
                            <div class="text-left">
                                <h2 class="text-white text-xl font-bold">开始换装</h2>
                                <p class="text-white text-sm opacity-90">上传照片，体验AI换装</p>
                            </div>
                        </div>
                    </button>
                </div>
                
                <!-- 每日签到 -->
                <div class="glass-card mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-gift text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold">每日签到</h3>
                                <p class="text-gray-300 text-sm">签到获得免费积分</p>
                            </div>
                        </div>
                        <button class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                            签到
                        </button>
                    </div>
                </div>
                
                <!-- 功能卡片 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="feature-card">
                        <div class="text-center">
                            <i class="fas fa-history text-2xl text-blue-400 mb-3"></i>
                            <h3 class="text-white font-semibold mb-1">换装历史</h3>
                            <p class="text-gray-400 text-xs">查看历史作品</p>
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="text-center">
                            <i class="fas fa-coins text-2xl text-yellow-400 mb-3"></i>
                            <h3 class="text-white font-semibold mb-1">积分中心</h3>
                            <p class="text-gray-400 text-xs">管理你的积分</p>
                        </div>
                    </div>
                </div>
                
                <!-- 最近换装 -->
                <div class="glass-card">
                    <h3 class="text-white font-semibold mb-4">最近换装</h3>
                    <div class="grid grid-cols-3 gap-3">
                        <div class="aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-lg"></i>
                        </div>
                        <div class="aspect-square bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-white text-lg"></i>
                        </div>
                        <div class="aspect-square bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-plus text-white text-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部导航 -->
            <div class="bottom-nav">
                <div class="flex">
                    <div class="nav-item active">
                        <i class="fas fa-home"></i>
                        <div class="text-xs">首页</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-magic"></i>
                        <div class="text-xs">换装</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-history"></i>
                        <div class="text-xs">历史</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-user"></i>
                        <div class="text-xs">我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
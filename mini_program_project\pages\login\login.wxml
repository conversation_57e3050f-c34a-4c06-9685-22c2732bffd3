<!--pages/login/login.wxml-->
<view class="container">
  <!-- 主要内容区域 -->
  <view class="content">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo-icon">
        <text class="iconfont icon-magic"></text>
      </view>
      <view class="logo-text">魔镜换装</view>
      <view class="logo-desc">开启你的时尚之旅</view>
    </view>
    
    <!-- 登录表单 -->
    <view class="login-form">
      <view class="input-group">
        <input 
          type="number" 
          placeholder="请输入手机号" 
          class="neon-input"
          value="{{phone}}"
          bindinput="onPhoneInput"
          maxlength="11"
        />
      </view>
      
      <view class="input-group">
        <view class="code-input-wrapper">
          <input 
            type="number" 
            placeholder="请输入验证码" 
            class="neon-input code-input"
            value="{{verifyCode}}"
            bindinput="onCodeInput"
            maxlength="6"
          />
          <button 
            class="code-button" 
            disabled="{{!canSendCode}}"
            bindtap="sendVerifyCode"
          >
            {{codeButtonText}}
          </button>
        </view>
      </view>
      
      <!-- 登录按钮 -->
      <button 
        class="neon-button login-button" 
        disabled="{{!canLogin}}"
        bindtap="handleLogin"
      >
        立即登录
      </button>
      
      <!-- 分割线 -->
      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">或</text>
        <view class="divider-line"></view>
      </view>
      
      <!-- 第三方登录 -->
      <view class="social-login">
        <button class="social-button" bindtap="handleWechatLogin">
          <text class="iconfont icon-wechat"></text>
          <text>微信一键登录</text>
        </button>
      </view>
      
      <!-- 用户协议 -->
      <view class="agreement">
        <text class="agreement-text">
          登录即表示同意
          <text class="link" bindtap="showUserAgreement">《用户协议》</text>
          和
          <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
        </text>
      </view>
    </view>
  </view>
</view>
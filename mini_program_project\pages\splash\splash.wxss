/* pages/splash/splash.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  position: relative;
  overflow: hidden;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 64rpx;
  position: relative;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 96rpx;
}

.logo-icon {
  margin-bottom: 48rpx;
}

.icon-magic {
  font-size: 120rpx;
  color: #8b5cf6;
}

.logo-text {
  font-size: 64rpx;
  font-weight: bold;
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16rpx;
}

.logo-desc {
  color: #cccccc;
  font-size: 28rpx;
}

/* 加载区域 */
.loading-section {
  margin-bottom: 64rpx;
}

.loading-text {
  text-align: center;
}

.loading-title {
  color: white;
  font-size: 36rpx;
  margin-bottom: 16rpx;
}

.loading-subtitle {
  color: #888888;
  font-size: 28rpx;
}

/* 版本信息 */
.version-info {
  position: absolute;
  bottom: 64rpx;
  text-align: center;
  color: #666666;
  font-size: 24rpx;
}
// pages/login/login.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    phone: '',
    verifyCode: '',
    canSendCode: false,
    canLogin: false,
    codeButtonText: '获取验证码',
    countdown: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 手机号输入
   */
  onPhoneInput(e) {
    const phone = e.detail.value
    this.setData({
      phone: phone,
      canSendCode: this.validatePhone(phone)
    })
    this.checkCanLogin()
  },

  /**
   * 验证码输入
   */
  onCodeInput(e) {
    const code = e.detail.value
    this.setData({
      verifyCode: code
    })
    this.checkCanLogin()
  },

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneReg = /^1[3-9]\d{9}$/
    return phoneReg.test(phone)
  },

  /**
   * 检查是否可以登录
   */
  checkCanLogin() {
    const { phone, verifyCode } = this.data
    const canLogin = this.validatePhone(phone) && verifyCode.length === 6
    this.setData({
      canLogin: canLogin
    })
  },

  /**
   * 发送验证码
   */
  sendVerifyCode() {
    if (!this.data.canSendCode) return

    const app = getApp()
    app.showLoading('发送中...')

    // 模拟发送验证码
    setTimeout(() => {
      app.hideLoading()
      app.showToast('验证码已发送', 'success')
      this.startCountdown()
    }, 1000)
  },

  /**
   * 开始倒计时
   */
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown: countdown,
      canSendCode: false,
      codeButtonText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          canSendCode: this.validatePhone(this.data.phone),
          codeButtonText: '获取验证码'
        })
      } else {
        this.setData({
          codeButtonText: `${countdown}s后重发`
        })
      }
    }, 1000)
  },

  /**
   * 处理登录
   */
  handleLogin() {
    if (!this.data.canLogin) return

    const app = getApp()
    app.showLoading('登录中...')

    // 模拟登录验证
    setTimeout(() => {
      app.hideLoading()
      
      // 保存登录状态
      wx.setStorageSync('isLogin', true)
      wx.setStorageSync('userPhone', this.data.phone)
      app.globalData.isLogin = true

      app.showToast('登录成功', 'success')
      
      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }, 1500)
    }, 2000)
  },

  /**
   * 微信登录
   */
  handleWechatLogin() {
    const app = getApp()
    
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        app.showLoading('登录中...')
        
        // 模拟微信登录
        setTimeout(() => {
          app.hideLoading()
          
          // 保存登录状态和用户信息
          wx.setStorageSync('isLogin', true)
          wx.setStorageSync('userInfo', res.userInfo)
          app.globalData.isLogin = true
          app.globalData.userInfo = res.userInfo

          app.showToast('登录成功', 'success')
          
          // 跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/home/<USER>'
            })
          }, 1500)
        }, 2000)
      },
      fail: (err) => {
        console.log('用户拒绝授权', err)
      }
    })
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议内容...',
      showCancel: false
    })
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策内容...',
      showCancel: false
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '魔镜换装 - AI智能换装应用',
      path: '/pages/login/login'
    }
  }
})
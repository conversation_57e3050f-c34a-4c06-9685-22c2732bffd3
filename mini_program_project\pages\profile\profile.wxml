<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 头部导航 -->
  <view class="nav-header">
    <view class="nav-title">个人中心</view>
    <text class="iconfont icon-settings nav-settings" bindtap="openSettings"></text>
  </view>
  
  <!-- 用户信息卡片 -->
  <view class="profile-header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || defaultAvatar}}" mode="aspectFill" />
      <view class="user-details">
        <view class="user-name-section">
          <view class="user-name">{{userInfo.nickName || '时尚达人'}}</view>
          <view class="vip-badge" wx:if="{{userInfo.isVip}}">VIP</view>
        </view>
        <view class="user-id">ID: {{userInfo.id || '123456789'}}</view>
        <view class="register-time">注册时间：{{userInfo.registerTime || '2024-01-01'}}</view>
      </view>
      <text class="iconfont icon-edit edit-btn" bindtap="editProfile"></text>
    </view>
  </view>
  
  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="stat-item" bindtap="goToHistory">
      <view class="stat-number">{{userStats.dressupCount}}</view>
      <view class="stat-label">换装次数</view>
    </view>
    <view class="stat-item" bindtap="goToPoints">
      <view class="stat-number">{{userStats.points}}</view>
      <view class="stat-label">剩余积分</view>
    </view>
    <view class="stat-item" bindtap="goToFavorites">
      <view class="stat-number">{{userStats.favoriteCount}}</view>
      <view class="stat-label">收藏作品</view>
    </view>
  </view>
  
  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="handleMenuTap" data-action="accountManage">
      <view class="menu-left">
        <view class="menu-icon account">
          <text class="iconfont icon-user"></text>
        </view>
        <view class="menu-text">
          <view class="menu-title">账户管理</view>
          <view class="menu-desc">个人信息设置</view>
        </view>
      </view>
      <text class="iconfont icon-arrow-right"></text>
    </view>
    
    <view class="menu-item" bindtap="handleMenuTap" data-action="myFavorites">
      <view class="menu-left">
        <view class="menu-icon favorites">
          <text class="iconfont icon-heart"></text>
        </view>
        <view class="menu-text">
          <view class="menu-title">我的收藏</view>
          <view class="menu-desc">收藏的换装作品</view>
        </view>
      </view>
      <text class="iconfont icon-arrow-right"></text>
    </view>
    
    <view class="menu-item" bindtap="handleMenuTap" data-action="pointsMall">
      <view class="menu-left">
        <view class="menu-icon points">
          <text class="iconfont icon-gift"></text>
        </view>
        <view class="menu-text">
          <view class="menu-title">积分商城</view>
          <view class="menu-desc">积分兑换礼品</view>
        </view>
      </view>
      <text class="iconfont icon-arrow-right"></text>
    </view>
    
    <view class="menu-item" bindtap="handleMenuTap" data-action="inviteFriends">
      <view class="menu-left">
        <view class="menu-icon invite">
          <text class="iconfont icon-users"></text>
        </view>
        <view class="menu-text">
          <view class="menu-title">邀请好友</view>
          <view class="menu-desc">邀请好友获得奖励</view>
        </view>
      </view>
      <text class="iconfont icon-arrow-right"></text>
    </view>
    
    <view class="menu-item" bindtap="handleMenuTap" data-action="customerService">
      <view class="menu-left">
        <view class="menu-icon service">
          <text class="iconfont icon-headset"></text>
        </view>
        <view class="menu-text">
          <view class="menu-title">客服支持</view>
          <view class="menu-desc">在线客服帮助</view>
        </view>
      </view>
      <text class="iconfont icon-arrow-right"></text>
    </view>
    
    <view class="menu-item" bindtap="handleMenuTap" data-action="aboutUs">
      <view class="menu-left">
        <view class="menu-icon about">
          <text class="iconfont icon-info"></text>
        </view>
        <view class="menu-text">
          <view class="menu-title">关于我们</view>
          <view class="menu-desc">版本信息和反馈</view>
        </view>
      </view>
      <text class="iconfont icon-arrow-right"></text>
    </view>
  </view>
  
  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
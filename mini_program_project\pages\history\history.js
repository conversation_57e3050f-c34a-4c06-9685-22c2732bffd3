// pages/history/history.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'all',
    filterTabs: [
      { key: 'all', name: '全部' },
      { key: 'week', name: '本周' },
      { key: 'month', name: '本月' },
      { key: 'favorite', name: '已收藏' }
    ],
    historyList: [],
    filteredHistoryList: [],
    totalCount: 0,
    weekCount: 0,
    favoriteCount: 0,
    hasMore: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadHistoryData()
    this.calculateStats()
  },

  /**
   * 加载历史数据
   */
  loadHistoryData() {
    // 从本地存储获取历史记录
    const historyList = wx.getStorageSync('dressupHistory') || []
    
    // 如果没有数据，生成一些模拟数据
    if (historyList.length === 0) {
      const mockData = this.generateMockData()
      wx.setStorageSync('dressupHistory', mockData)
      this.setData({
        historyList: mockData,
        filteredHistoryList: mockData
      })
    } else {
      this.setData({
        historyList: historyList,
        filteredHistoryList: historyList
      })
    }
    
    this.filterHistoryList()
  },

  /**
   * 生成模拟数据
   */
  generateMockData() {
    const mockImages = [
      'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=200&h=240&fit=crop',
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=240&fit=crop',
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=200&h=240&fit=crop',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=240&fit=crop'
    ]
    
    const mockTitles = ['优雅连衣裙', '职业套装', '休闲T恤', '时尚外套']
    
    return mockImages.map((image, index) => ({
      id: Date.now() + index,
      title: mockTitles[index],
      resultImage: image,
      createTime: this.formatDate(new Date(Date.now() - index * 24 * 60 * 60 * 1000)),
      type: index % 2 === 0 ? 'basic' : 'premium',
      isFavorite: index < 2
    }))
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const now = new Date()
    const target = new Date(date)
    const diffTime = now - target
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return target.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else if (diffDays === 1) {
      return '昨天 ' + target.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else {
      return target.toLocaleDateString('zh-CN').replace(/\//g, '-')
    }
  },

  /**
   * 计算统计数据
   */
  calculateStats() {
    const { historyList } = this.data
    const now = new Date()
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    
    let weekCount = 0
    let favoriteCount = 0
    
    historyList.forEach(item => {
      const createTime = new Date(item.createTime)
      if (createTime >= weekAgo) {
        weekCount++
      }
      if (item.isFavorite) {
        favoriteCount++
      }
    })
    
    this.setData({
      totalCount: historyList.length,
      weekCount,
      favoriteCount
    })
  },

  /**
   * 切换筛选标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
    this.filterHistoryList()
  },

  /**
   * 筛选历史列表
   */
  filterHistoryList() {
    const { activeTab, historyList } = this.data
    let filteredList = []
    
    switch (activeTab) {
      case 'all':
        filteredList = historyList
        break
      case 'week':
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        filteredList = historyList.filter(item => {
          return new Date(item.createTime) >= weekAgo
        })
        break
      case 'month':
        const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        filteredList = historyList.filter(item => {
          return new Date(item.createTime) >= monthAgo
        })
        break
      case 'favorite':
        filteredList = historyList.filter(item => item.isFavorite)
        break
    }
    
    this.setData({
      filteredHistoryList: filteredList
    })
  },

  /**
   * 切换收藏状态
   */
  toggleFavorite(e) {
    const id = e.currentTarget.dataset.id
    const { historyList } = this.data
    
    const updatedList = historyList.map(item => {
      if (item.id === id) {
        return { ...item, isFavorite: !item.isFavorite }
      }
      return item
    })
    
    this.setData({
      historyList: updatedList
    })
    
    // 更新本地存储
    wx.setStorageSync('dressupHistory', updatedList)
    
    // 重新筛选和计算统计
    this.filterHistoryList()
    this.calculateStats()
    
    const app = getApp()
    const item = updatedList.find(item => item.id === id)
    app.showToast(item.isFavorite ? '已收藏' : '取消收藏', 'success')
  },

  /**
   * 查看详情
   */
  viewDetail(e) {
    const item = e.currentTarget.dataset.item
    wx.previewImage({
      current: item.resultImage,
      urls: [item.resultImage]
    })
  },

  /**
   * 分享作品
   */
  shareWork(e) {
    const item = e.currentTarget.dataset.item
    
    wx.showActionSheet({
      itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
      success: (res) => {
        const app = getApp()
        switch (res.tapIndex) {
          case 0:
            app.showToast('分享到微信成功！', 'success')
            break
          case 1:
            app.showToast('分享到朋友圈成功！', 'success')
            break
          case 2:
            wx.setClipboardData({
              data: `${item.title} - 魔镜换装作品分享`,
              success: () => {
                app.showToast('链接已复制！', 'success')
              }
            })
            break
        }
      }
    })
  },

  /**
   * 重新编辑
   */
  editWork(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages/dressup/dressup?editId=${item.id}`
    })
  },

  /**
   * 保存到相册
   */
  saveToAlbum(e) {
    const item = e.currentTarget.dataset.item
    const app = getApp()
    
    wx.saveImageToPhotosAlbum({
      filePath: item.resultImage,
      success: () => {
        app.showToast('保存成功！', 'success')
      },
      fail: (err) => {
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要授权',
            content: '保存图片需要访问您的相册权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
            }
          })
        } else {
          app.showToast('保存失败', 'error')
        }
      }
    })
  },

  /**
   * 显示搜索
   */
  showSearch() {
    wx.showModal({
      title: '搜索功能',
      content: '搜索功能开发中，敬请期待...',
      showCancel: false
    })
  },

  /**
   * 跳转到换装页面
   */
  goToDressup() {
    wx.switchTab({
      url: '/pages/dressup/dressup'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时重新加载数据
    this.loadHistoryData()
    this.calculateStats()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadHistoryData()
    this.calculateStats()
    
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 模拟加载更多
    if (this.data.hasMore) {
      const app = getApp()
      app.showToast('暂无更多数据', 'none')
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的换装历史 - 魔镜换装',
      path: '/pages/history/history'
    }
  }
})
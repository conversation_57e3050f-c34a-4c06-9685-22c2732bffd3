<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔镜换装 - 启动页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        *::-webkit-scrollbar {
            display: none;
        }
        
        body {
            overflow: hidden;
        }
        
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .neon-glow {
            animation: neonGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes neonGlow {
            from {
                text-shadow: 0 0 10px #00d4ff, 0 0 20px #00d4ff, 0 0 30px #00d4ff;
            }
            to {
                text-shadow: 0 0 20px #ff006e, 0 0 30px #ff006e, 0 0 40px #ff006e;
            }
        }
        
        .loading-ring {
            width: 80px;
            height: 80px;
            border: 4px solid transparent;
            border-top: 4px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00d4ff;
            border-radius: 50%;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }
        
        .logo-text {
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="flex-1 flex flex-col items-center justify-center px-8 relative">
                <!-- 粒子效果 -->
                <div class="particle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
                <div class="particle" style="top: 30%; right: 15%; animation-delay: 1s;"></div>
                <div class="particle" style="bottom: 40%; left: 20%; animation-delay: 2s;"></div>
                <div class="particle" style="bottom: 30%; right: 10%; animation-delay: 0.5s;"></div>
                
                <!-- Logo区域 -->
                <div class="text-center mb-12">
                    <div class="mb-6">
                        <i class="fas fa-magic text-6xl text-purple-400 neon-glow"></i>
                    </div>
                    <h1 class="logo-text neon-glow mb-2">魔镜换装</h1>
                    <p class="text-gray-300 text-sm">AI智能换装，让美丽触手可及</p>
                </div>
                
                <!-- 加载动画 -->
                <div class="mb-8">
                    <div class="loading-ring"></div>
                </div>
                
                <!-- 加载文字 -->
                <div class="text-center">
                    <p class="text-white text-lg mb-2">正在启动魔镜</p>
                    <p class="text-gray-400 text-sm">Loading...</p>
                </div>
                
                <!-- 版本信息 -->
                <div class="absolute bottom-8 text-center">
                    <p class="text-gray-500 text-xs">Version 1.0.0</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔镜换装 - 积分中心</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        *::-webkit-scrollbar {
            display: none;
        }
        
        body {
            overflow: hidden;
        }
        
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .points-card {
            background: linear-gradient(135deg, #ff006e, #8b5cf6);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 0 30px rgba(255, 0, 110, 0.4);
        }
        
        .recharge-card {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .recharge-card:hover {
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        
        .recharge-card.popular {
            border-color: #ff006e;
            background: rgba(255, 0, 110, 0.1);
        }
        
        .ad-button {
            background: linear-gradient(45deg, #00d4ff, #ff006e);
            border-radius: 15px;
            padding: 15px;
            color: white;
            font-weight: bold;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
        }
        
        .ad-button:hover {
            box-shadow: 0 0 30px rgba(255, 0, 110, 0.7);
            transform: translateY(-2px);
        }
        
        .record-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #00d4ff;
        }
        
        .bottom-nav {
            background: rgba(255, 255, 255, 1);
            border-radius: 25px 25px 0 0;
            padding: 15px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            color: #00d4ff;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body class="bg-gray-900 flex items-center justify-center min-h-screen">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>
            
            <!-- 头部导航 -->
            <div class="flex items-center justify-between px-6 py-4">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-white text-lg font-semibold">积分中心</h1>
                <div class="w-6"></div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="flex-1 px-6 pb-24 overflow-y-auto">
                <!-- 积分余额卡片 -->
                <div class="points-card mb-6">
                    <div class="text-center">
                        <i class="fas fa-coins text-4xl text-yellow-300 mb-4"></i>
                        <h2 class="text-white text-3xl font-bold mb-2">128</h2>
                        <p class="text-white/80 text-lg">当前积分余额</p>
                    </div>
                </div>
                
                <!-- 免费获取积分 -->
                <div class="mb-6">
                    <h3 class="text-white text-lg font-semibold mb-4">免费获取积分</h3>
                    <button class="ad-button w-full">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-play-circle text-2xl"></i>
                                <div class="text-left">
                                    <p class="font-bold">观看广告</p>
                                    <p class="text-sm opacity-90">观看15秒广告获得1积分</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm">+1积分</p>
                            </div>
                        </div>
                    </button>
                </div>
                
                <!-- 充值套餐 -->
                <div class="mb-6">
                    <h3 class="text-white text-lg font-semibold mb-4">充值套餐</h3>
                    <div class="space-y-4">
                        <div class="recharge-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-white font-semibold text-lg">10积分</h4>
                                    <p class="text-gray-300 text-sm">基础套餐</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-white text-xl font-bold">¥6</p>
                                    <p class="text-gray-400 text-xs">¥0.6/积分</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recharge-card popular">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-white font-semibold text-lg">50积分</h4>
                                        <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">热门</span>
                                    </div>
                                    <p class="text-gray-300 text-sm">超值套餐</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-white text-xl font-bold">¥25</p>
                                    <p class="text-gray-400 text-xs">¥0.5/积分</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recharge-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-white font-semibold text-lg">100积分</h4>
                                    <p class="text-gray-300 text-sm">豪华套餐</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-white text-xl font-bold">¥45</p>
                                    <p class="text-gray-400 text-xs">¥0.45/积分</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 积分使用说明 -->
                <div class="mb-6">
                    <h3 class="text-white text-lg font-semibold mb-4">积分使用说明</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-magic text-blue-400"></i>
                            <span class="text-gray-300 text-sm">基础换装：1积分/次</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-star text-purple-400"></i>
                            <span class="text-gray-300 text-sm">高清换装：2积分/次</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-layer-group text-green-400"></i>
                            <span class="text-gray-300 text-sm">批量换装：5积分/次</span>
                        </div>
                    </div>
                </div>
                
                <!-- 消费记录 -->
                <div>
                    <h3 class="text-white text-lg font-semibold mb-4">最近消费记录</h3>
                    <div class="space-y-3">
                        <div class="record-item">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white font-semibold">基础换装</p>
                                    <p class="text-gray-400 text-xs">2024-01-15 14:30</p>
                                </div>
                                <span class="text-red-400 font-semibold">-1积分</span>
                            </div>
                        </div>
                        
                        <div class="record-item">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white font-semibold">观看广告</p>
                                    <p class="text-gray-400 text-xs">2024-01-15 10:15</p>
                                </div>
                                <span class="text-green-400 font-semibold">+1积分</span>
                            </div>
                        </div>
                        
                        <div class="record-item">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white font-semibold">充值</p>
                                    <p class="text-gray-400 text-xs">2024-01-14 16:20</p>
                                </div>
                                <span class="text-green-400 font-semibold">+50积分</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部导航 -->
            <div class="bottom-nav">
                <div class="flex">
                    <div class="nav-item">
                        <i class="fas fa-home"></i>
                        <div class="text-xs">首页</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-magic"></i>
                        <div class="text-xs">换装</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-history"></i>
                        <div class="text-xs">历史</div>
                    </div>
                    <div class="nav-item">
                        <i class="fas fa-user"></i>
                        <div class="text-xs">我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>